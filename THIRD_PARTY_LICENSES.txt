================================================================================
THIRD-PARTY SOFTWARE LICENSES
================================================================================

Project: OSI (Open Source Installer)
Project Path: /Users/<USER>/Documents/augment-projects/osi
Generated by: Universal Python License Reporter
Report Type: Runtime Dependencies (PyInstaller Bundled)

DEPENDENCY FILES ANALYZED:
  - /Users/<USER>/Documents/augment-projects/osi/requirements.txt
  - /Users/<USER>/Documents/augment-projects/osi/setup.py
  - /Users/<USER>/Documents/augment-projects/osi/pyproject.toml

SUMMARY:
  Total packages: 6
  Runtime packages: 6
  Packages requiring attribution: 6
  Packages with unknown licenses: 2

FILTERS APPLIED:
  - Runtime dependencies only (PyInstaller compliance mode)

EXCLUDED BUILD-TIME DEPENDENCIES:
The following build tools are excluded as they are not
distributed with the PyInstaller executable:
autopep8, bandit, black, build, conda, coverage, flake8, flit, hatch, ipykernel, ipython, isort, jupyter, mamba, mccabe, mkdocs, mypy, notebook, pdm, pip, pipenv, poetry, pre-commit, pycodestyle, pydocstyle, pyflakes, pyinstaller, pylint, pytest, safety, setuptools, sphinx, tox, twine, venv, virtualenv, wheel, yapf

================================================================================
PACKAGE DETAILS
================================================================================

Package: packaging
Version: 25.0
Version Spec: >=21.0
Dependency Type: runtime
License: unknown
Author: unknown
Homepage: unknown
Requires Attribution: Yes
----------------------------------------

Package: packaging
Version: 25.0
Version Spec: >=21.0
Dependency Type: runtime
License: unknown
Author: unknown
Homepage: unknown
Requires Attribution: Yes
----------------------------------------

Package: pkginfo
Version: ********
Version Spec: >=1.8.0
Dependency Type: runtime
License: MIT
Author: Tres Seaver, Agendaless Consulting
Homepage: https://code.launchpad.net/~tseaver/pkginfo/trunk
Requires Attribution: Yes
----------------------------------------

Package: pkginfo
Version: ********
Version Spec: >=1.8.0
Dependency Type: runtime
License: MIT
Author: Tres Seaver, Agendaless Consulting
Homepage: https://code.launchpad.net/~tseaver/pkginfo/trunk
Requires Attribution: Yes
----------------------------------------

Package: toml
Version: 0.10.2
Version Spec: >=0.10.2
Dependency Type: runtime
License: MIT
Author: William Pearson
Homepage: https://github.com/uiri/toml
Requires Attribution: Yes
----------------------------------------

Package: toml
Version: 0.10.2
Version Spec: >=0.10.2
Dependency Type: runtime
License: MIT
Author: William Pearson
Homepage: https://github.com/uiri/toml
Requires Attribution: Yes
----------------------------------------
