[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "osi"
description = "Python environment management for PyWheel applications"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Installation/Setup",
    "Topic :: System :: Software Distribution",
]
dependencies = [
    "toml>=0.10.2",
    "packaging>=21.0",
    "pip>=21.0.0",
    "setuptools>=50.0.0",
    "wheel>=0.36.0",
    "pkginfo>=1.8.0",
]
dynamic = ["version"]

[project.urls]
Homepage = "https://github.com/ethan-li/osi"
Repository = "https://github.com/ethan-li/osi"
Issues = "https://github.com/ethan-li/osi/issues"
Documentation = "https://github.com/ethan-li/osi/blob/main/README.md"

[project.scripts]
osi = "osi.launcher:main"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.0",
    "mypy>=0.812",
    "flake8>=3.8.0",
    "bandit>=1.7.0",
    "safety>=1.10.0",
    "pyinstaller>=5.0.0",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["osi*"]
exclude = ["tests*", "environments*", "build*", "dist*"]

[tool.setuptools.package-data]
osi = ["*.toml", "*.txt", "*.md"]

[tool.setuptools_scm]
write_to = "osi/_version.py"

[tool.black]
line-length = 88
target-version = ['py311', 'py312', 'py313']
include = '\.pyi?$'
extend-exclude = '''
/(
    # directories
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | environments
  | \.conda
  | \.pytest_cache
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["osi"]
known_third_party = ["toml", "packaging", "virtualenv", "pip", "setuptools", "wheel", "pkginfo"]
skip_glob = ["environments/*", "build/*", "dist/*", ".venv/*", ".tox/*"]
extend_skip = ["environments", "build", "dist"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["osi"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/environments/*",
    "*/_version.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "environments", "build", "dist"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "environments",
    ".venv",
    ".tox",
    ".eggs",
    "*.egg",
]
