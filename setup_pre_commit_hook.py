#!/usr/bin/env python3
"""
OSI Pre-Commit Hook Setup Script

This script sets up the pre-commit code quality validation as a Git pre-commit hook.
It creates the necessary hook files and ensures proper permissions.

Usage:
    python setup_pre_commit_hook.py              # Install standard hook
    python setup_pre_commit_hook.py --fast       # Install fast mode hook
    python setup_pre_commit_hook.py --remove     # Remove existing hook

Author: OSI Development Team
License: MIT
"""

import argparse
import os
import stat
import sys
from pathlib import Path


def create_pre_commit_hook(fast_mode: bool = False) -> bool:
    """Create the Git pre-commit hook file."""
    project_root = Path(__file__).parent
    git_hooks_dir = project_root / ".git" / "hooks"
    hook_file = git_hooks_dir / "pre-commit"

    # Check if .git directory exists
    if not git_hooks_dir.parent.exists():
        print("❌ Error: Not in a Git repository (.git directory not found)")
        return False

    # Create hooks directory if it doesn't exist
    git_hooks_dir.mkdir(exist_ok=True)

    # Determine the command to use
    if fast_mode:
        command = "python pre_commit_check.py --fast"
        mode_desc = "fast mode (skips tests and functional verification)"
    else:
        command = "python pre_commit_check.py"
        mode_desc = "full validation"

    # Create the hook script
    hook_content = f"""#!/bin/bash
#
# OSI Pre-Commit Code Quality Validation Hook
# 
# This hook runs comprehensive code quality checks before each commit
# to ensure professional standards are maintained.
#
# Generated by: setup_pre_commit_hook.py
# Mode: {mode_desc}
#

echo "🔍 Running OSI pre-commit code quality validation..."
echo "Mode: {mode_desc}"
echo ""

# Change to the project root directory
cd "$(git rev-parse --show-toplevel)"

# Run the validation tool
{command}

# Check the exit code
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Pre-commit validation failed!"
    echo "Please fix the issues above before committing."
    echo ""
    echo "💡 Tips:"
    echo "  - Use 'python pre_commit_check.py --fix' to auto-fix formatting"
    echo "  - Use 'git commit --no-verify' to bypass this hook (not recommended)"
    echo ""
    exit 1
fi

echo ""
echo "✅ Pre-commit validation passed! Proceeding with commit..."
echo ""
"""

    try:
        # Write the hook file
        with open(hook_file, "w") as f:
            f.write(hook_content)

        # Make the hook executable
        current_permissions = hook_file.stat().st_mode
        hook_file.chmod(current_permissions | stat.S_IEXEC)

        print(f"✅ Pre-commit hook installed successfully!")
        print(f"📁 Location: {hook_file}")
        print(f"🔧 Mode: {mode_desc}")
        print(f"")
        print(f"The hook will run automatically before each commit.")
        print(f"To bypass the hook (not recommended): git commit --no-verify")

        return True

    except Exception as e:
        print(f"❌ Error creating pre-commit hook: {e}")
        return False


def remove_pre_commit_hook() -> bool:
    """Remove the existing pre-commit hook."""
    project_root = Path(__file__).parent
    hook_file = project_root / ".git" / "hooks" / "pre-commit"

    if not hook_file.exists():
        print("ℹ️  No pre-commit hook found to remove")
        return True

    try:
        hook_file.unlink()
        print("✅ Pre-commit hook removed successfully!")
        return True
    except Exception as e:
        print(f"❌ Error removing pre-commit hook: {e}")
        return False


def check_dependencies() -> bool:
    """Check if required dependencies are available."""
    missing_tools = []

    # Check mypy
    try:
        import subprocess

        result = subprocess.run(
            ["python", "-m", "mypy", "--version"],
            capture_output=True,
            text=True,
            timeout=10,
        )
        if result.returncode != 0:
            missing_tools.append("mypy")
    except Exception:
        missing_tools.append("mypy")

    # Check black
    try:
        result = subprocess.run(
            ["python", "-m", "black", "--version"],
            capture_output=True,
            text=True,
            timeout=10,
        )
        if result.returncode != 0:
            missing_tools.append("black")
    except Exception:
        missing_tools.append("black")

    # Check isort
    try:
        result = subprocess.run(
            ["python", "-m", "isort", "--version"],
            capture_output=True,
            text=True,
            timeout=10,
        )
        if result.returncode != 0:
            missing_tools.append("isort")
    except Exception:
        missing_tools.append("isort")

    # Check yaml
    try:
        import yaml
    except ImportError:
        missing_tools.append("yaml")

    if missing_tools:
        print(f"❌ Missing required dependencies: {', '.join(missing_tools)}")
        print(f"")
        print(f"Please install missing dependencies:")
        if "mypy" in missing_tools:
            print(f"  pip install mypy types-toml")
        if "black" in missing_tools:
            print(f"  pip install black")
        if "isort" in missing_tools:
            print(f"  pip install isort")
        if "yaml" in missing_tools:
            print(f"  pip install PyYAML")
        return False

    print("✅ All required dependencies are available")
    return True


def main():
    """Main entry point for the setup script."""
    parser = argparse.ArgumentParser(
        description="OSI Pre-Commit Hook Setup Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python setup_pre_commit_hook.py              # Install standard hook (full validation)
  python setup_pre_commit_hook.py --fast       # Install fast mode hook (skip tests)
  python setup_pre_commit_hook.py --remove     # Remove existing hook
  
The pre-commit hook will run automatically before each commit and prevent
commits that don't meet code quality standards.

To bypass the hook temporarily (not recommended):
  git commit --no-verify
        """,
    )

    parser.add_argument(
        "--fast",
        action="store_true",
        help="Install hook in fast mode (skips test suite and functional verification)",
    )

    parser.add_argument(
        "--remove", action="store_true", help="Remove existing pre-commit hook"
    )

    args = parser.parse_args()

    print("🔧 OSI Pre-Commit Hook Setup")
    print("=" * 40)

    # Check if we're in the correct directory
    if not Path("osi").exists() or not Path("pre_commit_check.py").exists():
        print("❌ Error: This script must be run from the OSI project root directory")
        print("Expected to find 'osi/' directory and 'pre_commit_check.py' file")
        return 1

    if args.remove:
        success = remove_pre_commit_hook()
        return 0 if success else 1

    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        return 1

    print("")
    print("Installing pre-commit hook...")
    success = create_pre_commit_hook(fast_mode=args.fast)

    if success:
        print("")
        print("🎉 Setup completed successfully!")
        print("")
        print("Next steps:")
        print("1. Test the hook: python pre_commit_check.py")
        print("2. Make a commit to see the hook in action")
        print("3. Use 'python pre_commit_check.py --fix' to auto-fix formatting issues")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
