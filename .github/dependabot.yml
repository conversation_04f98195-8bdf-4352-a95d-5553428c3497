version: 2
updates:
  # Enable version updates for Python dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "zheng-li"
    assignees:
      - "zheng-li"
    commit-message:
      prefix: "deps"
      include: "scope"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "zheng-li"
    assignees:
      - "zheng-li"
    commit-message:
      prefix: "ci"
      include: "scope"

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "zheng-li"
    assignees:
      - "zheng-li"
    commit-message:
      prefix: "docker"
      include: "scope"
