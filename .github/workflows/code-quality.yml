name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy types-toml bandit safety

    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Check code formatting with black
      run: |
        black --check --diff .

    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .

    # Temporarily disabled due to environment inconsistencies
    # - name: Type checking with mypy
    #   run: |
    #     mypy osi/ --show-error-codes

    - name: Security check with bandit
      run: |
        bandit -r osi/ -f json -o bandit-report.json || true

    - name: Check dependencies with safety
      run: |
        safety check --json --output safety-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  documentation:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install sphinx sphinx-rtd-theme

    - name: Check documentation links
      run: |
        # Check for broken links in markdown files
        find . -name "*.md" -exec grep -l "http" {} \; | head -10

    - name: Validate README
      run: |
        python -c "
        import re
        with open('README.md', 'r') as f:
            content = f.read()
        # Check for basic structure
        assert '# OSI' in content
        assert '## Overview' in content
        assert '## Key Features' in content
        print('README.md validation passed')
        "

    - name: Check for TODO/FIXME comments
      run: |
        echo "Checking for TODO/FIXME comments..."
        grep -r "TODO\|FIXME\|XXX" --include="*.py" --include="*.md" . || echo "No TODO/FIXME comments found"
