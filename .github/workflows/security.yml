name: Security

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security checks weekly on Mondays at 9 AM UTC
    - cron: '0 9 * * 1'

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit safety semgrep

    - name: Run Bandit security scan
      run: |
        bandit -r osi/ -f json -o bandit-results.json
      continue-on-error: true

    - name: Run Safety dependency check
      run: |
        safety check --json --output safety-results.json
      continue-on-error: true

    - name: Run Semgrep security scan
      run: |
        semgrep --config=auto --json --output=semgrep-results.json osi/
      continue-on-error: true

    - name: Upload security scan results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-scan-results
        path: |
          bandit-results.json
          safety-results.json
          semgrep-results.json

    - name: Check for high severity issues
      run: |
        echo "Checking for high severity security issues..."
        if [ -f bandit-results.json ]; then
          # Count high severity issues using grep and wc
          HIGH_ISSUES=$(grep -c '"issue_severity": "HIGH"' bandit-results.json || echo "0")
          if [ "$HIGH_ISSUES" -gt 0 ]; then
            echo "Found $HIGH_ISSUES high severity security issues!"
            exit 1
          else
            echo "No high severity security issues found."
          fi
        else
          echo "No bandit results file found, skipping high severity check"
        fi

  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'python' ]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:${{matrix.language}}"
